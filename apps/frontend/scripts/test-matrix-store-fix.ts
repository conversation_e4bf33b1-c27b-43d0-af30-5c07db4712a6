/**
 * MatrixStore 修复验证测试脚本
 * 🎯 目标：验证数据查找问题修复和性能优化效果
 * 📊 测试内容：数据源访问、坐标查询、性能分析、边界情况处理
 */

import { useMatrixStore } from '../core/matrix/MatrixStore';
import { MATRIX_SIZE } from '../core/matrix/MatrixTypes';

// 测试配置
const TEST_CONFIG = {
  PERFORMANCE_THRESHOLD_MS: 100,
  SAMPLE_COORDINATES: [
    [0, 0],     // 左上角
    [16, 16],   // 中心点
    [32, 32],   // 右下角
    [32, 9],    // 问题坐标1
    [32, 15],   // 问题坐标2
    [32, 32],   // 问题坐标3
    [15, 15],   // 中心附近
    [8, 8],     // 第一象限
    [24, 24],   // 第三象限
  ] as [number, number][]
};

/**
 * 测试数据源访问修复
 */
function testDataSourceAccess() {
  console.log('\n🔍 测试1: 数据源访问修复');
  console.log('=' .repeat(50));
  
  const store = useMatrixStore.getState();
  
  try {
    // 测试修复后的数据访问
    const stats = store.getMatrixDataStats();
    
    console.log('✅ 数据源访问成功');
    console.log('📊 数据统计:', {
      totalDataPoints: stats.totalDataPoints,
      coverageRange: stats.coverageRange,
      coveragePercentage: stats.coveragePercentage
    });
    
    // 验证数据结构
    if (stats.totalDataPoints > 0) {
      console.log('✅ 数据点总数正常:', stats.totalDataPoints);
    } else {
      console.error('❌ 数据点总数异常:', stats.totalDataPoints);
    }
    
    return true;
  } catch (error) {
    console.error('❌ 数据源访问失败:', error);
    return false;
  }
}

/**
 * 测试坐标查询功能
 */
function testCoordinateQueries() {
  console.log('\n🎯 测试2: 坐标查询功能');
  console.log('=' .repeat(50));
  
  const store = useMatrixStore.getState();
  let successCount = 0;
  let failCount = 0;
  
  TEST_CONFIG.SAMPLE_COORDINATES.forEach(([x, y]) => {
    const dataPoint = store.getMatrixDataByCoordinate(x, y);
    const hasData = store.hasMatrixData(x, y);
    
    console.log(`坐标 (${x},${y}):`, {
      hasData,
      dataPoint: dataPoint ? {
        color: dataPoint.color,
        level: dataPoint.level,
        group: dataPoint.group
      } : null
    });
    
    if (dataPoint) {
      successCount++;
    } else {
      failCount++;
    }
  });
  
  console.log('\n📈 查询结果统计:');
  console.log(`✅ 成功查询: ${successCount}`);
  console.log(`❌ 无数据坐标: ${failCount}`);
  console.log(`📊 成功率: ${((successCount / TEST_CONFIG.SAMPLE_COORDINATES.length) * 100).toFixed(1)}%`);
  
  return { successCount, failCount };
}

/**
 * 测试初始化性能
 */
async function testInitializationPerformance() {
  console.log('\n⚡ 测试3: 初始化性能');
  console.log('=' .repeat(50));
  
  const store = useMatrixStore.getState();
  
  // 清除现有数据
  store.clearMatrix();
  
  // 测试初始化性能
  const startTime = performance.now();
  store.initializeMatrix();
  const endTime = performance.now();
  
  const initTime = endTime - startTime;
  
  console.log(`⏱️ 初始化耗时: ${initTime.toFixed(2)}ms`);
  
  if (initTime <= TEST_CONFIG.PERFORMANCE_THRESHOLD_MS) {
    console.log(`✅ 性能达标 (≤ ${TEST_CONFIG.PERFORMANCE_THRESHOLD_MS}ms)`);
    return true;
  } else {
    console.log(`⚠️ 性能超标 (> ${TEST_CONFIG.PERFORMANCE_THRESHOLD_MS}ms)`);
    return false;
  }
}

/**
 * 测试查询性能分析
 */
function testQueryPerformanceAnalysis() {
  console.log('\n📊 测试4: 查询性能分析');
  console.log('=' .repeat(50));
  
  const store = useMatrixStore.getState();
  
  // 运行性能分析
  const results = store.analyzeQueryPerformance(200);
  
  console.log('🔍 性能分析结果:');
  console.log(`总查询数: ${results.totalQueries}`);
  console.log(`成功查询: ${results.successfulQueries}`);
  console.log(`失败查询: ${results.failedQueries}`);
  console.log(`平均查询时间: ${results.averageQueryTime.toFixed(4)}ms`);
  console.log(`成功率: ${((results.successfulQueries / results.totalQueries) * 100).toFixed(1)}%`);
  
  return results;
}

/**
 * 测试边界情况处理
 */
function testBoundaryConditions() {
  console.log('\n🔒 测试5: 边界情况处理');
  console.log('=' .repeat(50));
  
  const store = useMatrixStore.getState();
  
  const boundaryTests = [
    { name: '左上角', x: 0, y: 0 },
    { name: '右上角', x: MATRIX_SIZE - 1, y: 0 },
    { name: '左下角', x: 0, y: MATRIX_SIZE - 1 },
    { name: '右下角', x: MATRIX_SIZE - 1, y: MATRIX_SIZE - 1 },
    { name: '超出边界-负数', x: -1, y: -1 },
    { name: '超出边界-正数', x: MATRIX_SIZE, y: MATRIX_SIZE },
  ];
  
  boundaryTests.forEach(test => {
    try {
      const hasData = store.hasMatrixData(test.x, test.y);
      const dataPoint = store.getMatrixDataByCoordinate(test.x, test.y);
      
      console.log(`${test.name} (${test.x},${test.y}):`, {
        hasData,
        dataPoint: dataPoint ? '有数据' : '无数据'
      });
    } catch (error) {
      console.log(`${test.name} (${test.x},${test.y}): ❌ 错误 -`, (error as Error).message);
    }
  });
}

/**
 * 主测试函数
 */
export async function runMatrixStoreFixTests() {
  console.log('🚀 开始 MatrixStore 修复验证测试');
  console.log('=' .repeat(60));
  
  const results = {
    dataSourceAccess: false,
    coordinateQueries: { successCount: 0, failCount: 0 },
    initializationPerformance: false,
    queryPerformance: null as any,
    boundaryConditions: true
  };
  
  try {
    // 测试1: 数据源访问
    results.dataSourceAccess = testDataSourceAccess();
    
    // 测试2: 坐标查询
    results.coordinateQueries = testCoordinateQueries();
    
    // 测试3: 初始化性能
    results.initializationPerformance = await testInitializationPerformance();
    
    // 测试4: 查询性能分析
    results.queryPerformance = testQueryPerformanceAnalysis();
    
    // 测试5: 边界情况
    testBoundaryConditions();
    
    // 总结报告
    console.log('\n📋 测试总结报告');
    console.log('=' .repeat(60));
    console.log('✅ 数据源访问:', results.dataSourceAccess ? '通过' : '失败');
    console.log('✅ 坐标查询功能:', `${results.coordinateQueries.successCount}/${TEST_CONFIG.SAMPLE_COORDINATES.length} 成功`);
    console.log('✅ 初始化性能:', results.initializationPerformance ? '达标' : '超标');
    console.log('✅ 查询性能:', `平均 ${results.queryPerformance?.averageQueryTime?.toFixed(4)}ms`);
    console.log('✅ 边界条件处理:', '正常');
    
    const overallSuccess = results.dataSourceAccess && 
                          results.initializationPerformance && 
                          results.coordinateQueries.successCount > 0;
    
    console.log('\n🎯 总体结果:', overallSuccess ? '✅ 修复成功' : '❌ 仍有问题');
    
    return results;
    
  } catch (error) {
    console.error('❌ 测试执行失败:', error);
    return null;
  }
}

// 如果直接运行此脚本
if (typeof window !== 'undefined') {
  // 浏览器环境
  (window as any).runMatrixStoreFixTests = runMatrixStoreFixTests;
  console.log('💡 在浏览器控制台中运行: runMatrixStoreFixTests()');
}
