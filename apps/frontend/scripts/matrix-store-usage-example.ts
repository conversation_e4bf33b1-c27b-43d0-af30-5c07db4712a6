/**
 * MatrixStore 使用示例 - 展示修复后的功能
 * 🎯 目标：演示修复后的 MatrixStore 的正确使用方法
 * 📚 内容：基本操作、性能监控、数据分析、调试工具
 */

import { useMatrixStore } from '../core/matrix/MatrixStore';

/**
 * 基本使用示例
 */
export function basicUsageExample() {
  console.log('📚 MatrixStore 基本使用示例');
  console.log('=' .repeat(40));
  
  // 获取store实例
  const store = useMatrixStore.getState();
  
  // 1. 初始化矩阵（现在性能已优化）
  console.log('🚀 开始初始化矩阵...');
  const startTime = performance.now();
  store.initializeMatrix();
  const endTime = performance.now();
  console.log(`✅ 初始化完成，耗时: ${(endTime - startTime).toFixed(2)}ms`);
  
  // 2. 查询特定坐标的数据
  console.log('\n🔍 查询坐标数据:');
  const testCoordinates = [[16, 16], [32, 9], [8, 8], [24, 24]];
  
  testCoordinates.forEach(([x, y]) => {
    const hasData = store.hasMatrixData(x, y);
    const dataPoint = store.getMatrixDataByCoordinate(x, y);
    
    console.log(`坐标 (${x},${y}):`, {
      hasData,
      color: dataPoint?.color || '无',
      level: dataPoint?.level || '无',
      group: dataPoint?.group || '无'
    });
  });
  
  // 3. 获取数据统计信息
  console.log('\n📊 数据统计信息:');
  const stats = store.getMatrixDataStats();
  console.log('总数据点:', stats.totalDataPoints);
  console.log('覆盖范围:', stats.coverageRange);
  console.log('覆盖率:', stats.coveragePercentage);
  console.log('颜色分布:', stats.colorDistribution);
}

/**
 * 性能监控示例
 */
export function performanceMonitoringExample() {
  console.log('\n⚡ 性能监控示例');
  console.log('=' .repeat(40));
  
  const store = useMatrixStore.getState();
  
  // 1. 查询性能分析
  console.log('🔍 运行查询性能分析...');
  const perfResults = store.analyzeQueryPerformance(100);
  
  console.log('性能分析结果:');
  console.log(`- 总查询数: ${perfResults.totalQueries}`);
  console.log(`- 成功率: ${((perfResults.successfulQueries / perfResults.totalQueries) * 100).toFixed(1)}%`);
  console.log(`- 平均查询时间: ${perfResults.averageQueryTime.toFixed(4)}ms`);
  
  // 2. 批量操作性能测试
  console.log('\n📦 批量操作性能测试...');
  const batchStart = performance.now();
  
  // 模拟批量查询
  const batchResults = [];
  for (let i = 0; i < 1000; i++) {
    const x = Math.floor(Math.random() * 33);
    const y = Math.floor(Math.random() * 33);
    batchResults.push(store.hasMatrixData(x, y));
  }
  
  const batchEnd = performance.now();
  const successCount = batchResults.filter(Boolean).length;
  
  console.log(`批量查询结果:`);
  console.log(`- 查询数量: 1000`);
  console.log(`- 总耗时: ${(batchEnd - batchStart).toFixed(2)}ms`);
  console.log(`- 平均耗时: ${((batchEnd - batchStart) / 1000).toFixed(4)}ms/查询`);
  console.log(`- 命中率: ${(successCount / 1000 * 100).toFixed(1)}%`);
}

/**
 * 数据分析示例
 */
export function dataAnalysisExample() {
  console.log('\n📈 数据分析示例');
  console.log('=' .repeat(40));
  
  const store = useMatrixStore.getState();
  const stats = store.getMatrixDataStats();
  
  // 1. 颜色分布分析
  console.log('🎨 颜色分布分析:');
  Object.entries(stats.colorDistribution).forEach(([color, count]) => {
    const countNum = count as number;
    if (countNum > 0) {
      const percentage = ((countNum / stats.totalDataPoints) * 100).toFixed(1);
      console.log(`- ${color}: ${countNum} (${percentage}%)`);
    }
  });
  
  // 2. 层级分布分析
  console.log('\n📊 层级分布分析:');
  Object.entries(stats.levelDistribution).forEach(([level, count]) => {
    const countNum = count as number;
    if (countNum > 0) {
      const percentage = ((countNum / stats.totalDataPoints) * 100).toFixed(1);
      console.log(`- Level ${level}: ${countNum} (${percentage}%)`);
    }
  });
  
  // 3. 覆盖范围分析
  console.log('\n🗺️ 覆盖范围分析:');
  console.log(`X轴范围: ${stats.coverageRange.x.min} - ${stats.coverageRange.x.max} (跨度: ${stats.coverageRange.x.span})`);
  console.log(`Y轴范围: ${stats.coverageRange.y.min} - ${stats.coverageRange.y.max} (跨度: ${stats.coverageRange.y.span})`);
  console.log(`总覆盖率: ${stats.coveragePercentage}`);
  
  // 4. 行覆盖分析
  console.log('\n📏 行覆盖分析 (前10行):');
  const rowEntries = Object.entries(stats.rowCoverage)
    .sort(([a], [b]) => {
      const rowA = parseInt(a.split('_')[1]);
      const rowB = parseInt(b.split('_')[1]);
      return rowA - rowB;
    })
    .slice(0, 10);
    
  rowEntries.forEach(([rowKey, data]) => {
    const rowNum = rowKey.split('_')[1];
    const dataObj = data as { count: number };
    console.log(`- 第${rowNum}行: ${dataObj.count}个数据点`);
  });
}

/**
 * 调试工具示例
 */
export function debuggingToolsExample() {
  console.log('\n🔧 调试工具示例');
  console.log('=' .repeat(40));
  
  const store = useMatrixStore.getState();
  
  // 1. 检查特定区域的数据
  console.log('🔍 检查边界区域数据:');
  const boundaryRegions = [
    { name: '左上角', coords: [[0, 0], [1, 1], [2, 2]] },
    { name: '右下角', coords: [[30, 30], [31, 31], [32, 32]] },
    { name: '中心区域', coords: [[15, 15], [16, 16], [17, 17]] },
    { name: '问题区域', coords: [[32, 9], [32, 15], [32, 32]] }
  ];
  
  boundaryRegions.forEach(region => {
    console.log(`\n${region.name}:`);
    region.coords.forEach(([x, y]) => {
      const hasData = store.hasMatrixData(x, y);
      const dataPoint = store.getMatrixDataByCoordinate(x, y);
      console.log(`  (${x},${y}): ${hasData ? '有数据' : '无数据'}${dataPoint ? ` - ${dataPoint.color} L${dataPoint.level}` : ''}`);
    });
  });
  
  // 2. 验证数据一致性
  console.log('\n✅ 数据一致性验证:');
  const stats = store.getMatrixDataStats();
  const totalFromDistribution = Object.values(stats.colorDistribution).reduce((sum, count) => (sum as number) + (count as number), 0);
  
  console.log(`统计总数: ${stats.totalDataPoints}`);
  console.log(`分布总数: ${totalFromDistribution}`);
  console.log(`数据一致性: ${stats.totalDataPoints === totalFromDistribution ? '✅ 一致' : '❌ 不一致'}`);
}

/**
 * 完整示例运行器
 */
export function runCompleteExample() {
  console.log('🎯 MatrixStore 完整功能示例');
  console.log('=' .repeat(60));
  
  try {
    // 运行所有示例
    basicUsageExample();
    performanceMonitoringExample();
    dataAnalysisExample();
    debuggingToolsExample();
    
    console.log('\n🎉 所有示例运行完成！');
    console.log('💡 提示: 这些功能现在都已经过性能优化，可以安全地在生产环境中使用。');
    
  } catch (error) {
    console.error('❌ 示例运行失败:', error);
  }
}

// 导出给浏览器环境使用
if (typeof window !== 'undefined') {
  (window as any).matrixStoreExamples = {
    basicUsage: basicUsageExample,
    performanceMonitoring: performanceMonitoringExample,
    dataAnalysis: dataAnalysisExample,
    debuggingTools: debuggingToolsExample,
    runComplete: runCompleteExample
  };
  
  console.log('💡 在浏览器控制台中使用:');
  console.log('- matrixStoreExamples.basicUsage()');
  console.log('- matrixStoreExamples.performanceMonitoring()');
  console.log('- matrixStoreExamples.dataAnalysis()');
  console.log('- matrixStoreExamples.debuggingTools()');
  console.log('- matrixStoreExamples.runComplete()');
}
